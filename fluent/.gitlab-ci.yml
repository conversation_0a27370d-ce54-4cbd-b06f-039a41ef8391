variables:
  # older versions of git have issues fetching.
  GIT_STRATEGY: clone


before_script:
  - docker info
  - '[ -d tmp ] || mkdir tmp'
  - git clone https://github.com/OSC/ondemand-packaging.git tmp/ondemand-packaging
  - cp /systems/osc_certs/gpg/ondemand/.gpgpass $CI_PROJECT_DIR/tmp/ondemand-packaging/
  - cp /systems/osc_certs/gpg/ondemand/ondemand.sec $CI_PROJECT_DIR/tmp/ondemand-packaging/
stages:
  - build
  - deploy

rpm-build:
  stage: build
  only:
    - tags
  script:
    - ./tmp/ondemand-packaging/build.sh -w $CI_PROJECT_DIR/tmp/work -o $CI_PROJECT_DIR/tmp/output -V $CI_COMMIT_TAG -u -v $CI_PROJECT_DIR/packaging
  artifacts:
    paths:
      - tmp/output
    name: "$CI_PROJECT_NAME-$CI_COMMIT_TAG"


rpm-deploy-ci:
  stage: deploy
  only:
    - tags
  script:
    - ./tmp/ondemand-packaging/release.py --debug --pkey /systems/osc_certs/ssh/ondemand-packaging/id_rsa -c ci ./tmp/output/*
rpm-deploy:
  stage: deploy
  only:
    - tags
  except:
    variables:
      - $CI_COMMIT_TAG =~ /.*_.*/
  script:
    - ./tmp/ondemand-packaging/release.py --debug --pkey /systems/osc_certs/ssh/ondemand-packaging/id_rsa -c main ./tmp/output/*
