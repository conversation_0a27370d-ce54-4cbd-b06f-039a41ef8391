#!/usr/bin/env bash

# Clean the environment
module purge

# Set working directory to home directory
#cd "${HOME}"
cd "${SLURM_SUBMIT_DIR}"
echo "${SLURM_SUBMIT_DIR}"

module load fluent/2019

scontrol show hostname $SLURM_JOB_NODELIST | perl -ne 'chomb; print "$_"x1' &>> ./hosts_file
echo 1
# Launch MATLAB
#nohup  /home/<USER>/ansys-2019/ansys_inc/v190/fluent/bin/fluent <%= context.dime %> -t <%= context.num_cores %> -mpi=ibmpi -pinfiniband -cnf=./hosts_file -ssh &
nohup  /home/<USER>/ansys-2019/ansys_inc/v190/fluent/bin/fluent <%= context.dime %> -t <%= context.num_cores %> -ssh &
module list

nohup ./desktops/scancel.sh &
set -x
#
# Launch Xfce Window Manager and Panel
#
export SHELL="$(getent passwd $USER | cut -d: -f7)"
echo 2
echo "Launching desktop '<%= context.desktop %>'..."
source "<%= session.staged_root.join("desktops", "#{context.desktop}.sh") %>"
echo "Desktop '<%= context.desktop %>' ended..."
#
# Start MATLAB
#
echo 3
# Load the required environment

echo $SLURM_JOB_ID

#退出应用
#

